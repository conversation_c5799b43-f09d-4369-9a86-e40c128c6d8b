const express = require('express');
const cors = require('cors');
const path = require('path');

// Simple Express server without <PERSON>risma for testing
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'TC Platform API is running (simple mode)'
  });
});

// Simple auth endpoints for testing
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  // Simple mock authentication
  if (email && password) {
    res.json({
      success: true,
      data: {
        user: {
          id: '1',
          email: email,
          firstName: 'Test',
          lastName: 'User',
          role: 'TC'
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Email and password required'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { email, password, firstName, lastName } = req.body;

  if (email && password && firstName && lastName) {
    res.json({
      success: true,
      data: {
        user: {
          id: '2',
          email: email,
          firstName: firstName,
          lastName: lastName,
          role: 'TC'
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }
});

app.get('/api/auth/me', (req, res) => {
  res.json({
    success: true,
    data: {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'TC'
    }
  });
});

app.post('/api/auth/refresh', (req, res) => {
  res.json({
    success: true,
    data: {
      token: 'new-mock-jwt-token'
    }
  });
});

// Simple users endpoint
app.get('/api/users/me', (req, res) => {
  res.json({
    success: true,
    data: {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'TC'
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'TC Platform API (Simple Mode)',
    version: '1.0.0',
    status: 'running'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 TC Platform API (Simple Mode) running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}`);
});
