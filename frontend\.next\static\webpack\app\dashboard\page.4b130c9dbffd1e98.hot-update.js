"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/DashboardOverview.tsx":
/*!********************************************************!*\
  !*** ./src/components/dashboard/DashboardOverview.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardOverview: function() { return /* binding */ DashboardOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _store_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/authStore */ \"(app-pages-browser)/./src/store/authStore.ts\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StatsCard */ \"(app-pages-browser)/./src/components/dashboard/StatsCard.tsx\");\n/* harmony import */ var _RecentActivity__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RecentActivity */ \"(app-pages-browser)/./src/components/dashboard/RecentActivity.tsx\");\n/* harmony import */ var _UpcomingDeadlines__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UpcomingDeadlines */ \"(app-pages-browser)/./src/components/dashboard/UpcomingDeadlines.tsx\");\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./src/components/dashboard/QuickActions.tsx\");\n/* harmony import */ var _RecentCommunication__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./RecentCommunication */ \"(app-pages-browser)/./src/components/dashboard/RecentCommunication.tsx\");\n/* harmony import */ var _QuickCommunication__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./QuickCommunication */ \"(app-pages-browser)/./src/components/dashboard/QuickCommunication.tsx\");\n/* harmony import */ var _CommunicationStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CommunicationStats */ \"(app-pages-browser)/./src/components/dashboard/CommunicationStats.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,DocumentTextIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,DocumentTextIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,DocumentTextIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,DocumentTextIcon,FolderIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/**\n * Dashboard overview component\n */ /* __next_internal_client_entry_do_not_use__ DashboardOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data - in real app, this would come from API\nconst mockStats = [\n    {\n        name: \"Active Transactions\",\n        value: \"12\",\n        change: \"+2\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Pending Tasks\",\n        value: \"8\",\n        change: \"-3\",\n        changeType: \"decrease\",\n        icon: _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"Documents\",\n        value: \"156\",\n        change: \"+12\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Active Contacts\",\n        value: \"34\",\n        change: \"+5\",\n        changeType: \"increase\",\n        icon: _barrel_optimize_names_CheckIcon_DocumentTextIcon_FolderIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nconst mockRecentActivity = [\n    {\n        id: \"1\",\n        type: \"transaction_created\",\n        title: \"New transaction created\",\n        description: \"123 Main St, Philadelphia, PA\",\n        timestamp: \"2 hours ago\",\n        user: \"John Doe\"\n    },\n    {\n        id: \"2\",\n        type: \"task_completed\",\n        title: \"Task completed\",\n        description: \"Property inspection scheduled\",\n        timestamp: \"4 hours ago\",\n        user: \"Sarah Johnson\"\n    },\n    {\n        id: \"3\",\n        type: \"document_uploaded\",\n        title: \"Document uploaded\",\n        description: \"Purchase agreement signed\",\n        timestamp: \"6 hours ago\",\n        user: \"Mike Chen\"\n    }\n];\nconst mockUpcomingDeadlines = [\n    {\n        id: \"1\",\n        title: \"Property Inspection\",\n        transaction: \"123 Main St\",\n        dueDate: \"2024-01-15\",\n        priority: \"high\",\n        daysUntil: 2\n    },\n    {\n        id: \"2\",\n        title: \"Financing Approval\",\n        transaction: \"456 Oak Ave\",\n        dueDate: \"2024-01-18\",\n        priority: \"medium\",\n        daysUntil: 5\n    },\n    {\n        id: \"3\",\n        title: \"Final Walkthrough\",\n        transaction: \"789 Pine St\",\n        dueDate: \"2024-01-20\",\n        priority: \"high\",\n        daysUntil: 7\n    }\n];\nfunction DashboardOverview() {\n    _s();\n    const { user } = (0,_store_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: [\n                            \"Welcome back, \",\n                            user === null || user === void 0 ? void 0 : user.firstName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Here's what's happening with your transactions today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: mockStats.map((stat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_2__.StatsCard, {\n                        ...stat\n                    }, stat.name, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RecentActivity__WEBPACK_IMPORTED_MODULE_3__.RecentActivity, {\n                                activities: mockRecentActivity\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RecentCommunication__WEBPACK_IMPORTED_MODULE_6__.RecentCommunication, {\n                                currentUserId: user === null || user === void 0 ? void 0 : user.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpcomingDeadlines__WEBPACK_IMPORTED_MODULE_4__.UpcomingDeadlines, {\n                                deadlines: mockUpcomingDeadlines\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_5__.QuickActions, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickCommunication__WEBPACK_IMPORTED_MODULE_7__.QuickCommunication, {\n                                currentUserId: user === null || user === void 0 ? void 0 : user.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommunicationStats__WEBPACK_IMPORTED_MODULE_8__.CommunicationStats, {\n                                currentUserId: user === null || user === void 0 ? void 0 : user.id\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\Development\\\\tcb2b\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardOverview.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardOverview, \"aAjLQdegBonWw6v8amJtA1nd/MY=\", false, function() {\n    return [\n        _store_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore\n    ];\n});\n_c = DashboardOverview;\nvar _c;\n$RefreshReg$(_c, \"DashboardOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/DashboardOverview.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 12.75 6 6 9-13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CheckIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NoZWNrSWNvbi5qcyIsIm1hcHBpbmdzIjoiOztBQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsK0JBQStCLGdEQUFtQjtBQUNyRDtBQUNBLEdBQUcsOEJBQThCLGdEQUFtQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxpQ0FBaUMsNkNBQWdCO0FBQ2pELCtEQUFlLFVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vQ2hlY2tJY29uLmpzP2RkZDAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBDaGVja0ljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIm00LjUgMTIuNzUgNiA2IDktMTMuNVwiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoQ2hlY2tJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\n"));

/***/ })

});