/**
 * Brokerages page - Admin only
 */

'use client';

import { useState } from 'react';
import { BuildingOfficeIcon, PencilIcon, TrashIcon, UserGroupIcon } from '@heroicons/react/24/outline';

// Mock data
const mockBrokerages = [
  {
    id: '1',
    name: 'ABC Realty',
    address: '123 Business St, City, ST 12345',
    phone: '(*************',
    email: '<EMAIL>',
    userCount: 15,
    activeTransactions: 8,
    status: 'active',
    createdAt: '2023-06-15',
  },
  {
    id: '2',
    name: 'XYZ Properties',
    address: '456 Commerce Ave, City, ST 12345',
    phone: '(*************',
    email: '<EMAIL>',
    userCount: 23,
    activeTransactions: 12,
    status: 'active',
    createdAt: '2023-08-22',
  },
  {
    id: '3',
    name: 'Premier Homes',
    address: '789 Market Blvd, City, ST 12345',
    phone: '(*************',
    email: '<EMAIL>',
    userCount: 8,
    activeTransactions: 3,
    status: 'inactive',
    createdAt: '2023-12-01',
  },
];

const statusColors = {
  active: 'text-green-600 bg-green-100',
  inactive: 'text-gray-600 bg-gray-100',
  suspended: 'text-red-600 bg-red-100',
};

export default function BrokeragesPage() {
  const [filter, setFilter] = useState('all');

  const filteredBrokerages = mockBrokerages.filter(brokerage => {
    if (filter === 'all') return true;
    return brokerage.status === filter;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Brokerages</h1>
            <p className="text-gray-600 mt-1">
              Manage brokerage accounts and settings.
            </p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Add Brokerage
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <BuildingOfficeIcon className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Brokerages</p>
              <p className="text-2xl font-bold text-gray-900">{mockBrokerages.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">
                {mockBrokerages.reduce((sum, b) => sum + b.userCount, 0)}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center">
            <BuildingOfficeIcon className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Transactions</p>
              <p className="text-2xl font-bold text-gray-900">
                {mockBrokerages.reduce((sum, b) => sum + b.activeTransactions, 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex space-x-4">
          {['all', 'active', 'inactive', 'suspended'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === status
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Brokerages Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredBrokerages.map((brokerage) => (
          <div key={brokerage.id} className="bg-white shadow rounded-lg p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{brokerage.name}</h3>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      statusColors[brokerage.status as keyof typeof statusColors]
                    }`}
                  >
                    {brokerage.status}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="text-blue-600 hover:text-blue-900">
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button className="text-red-600 hover:text-red-900">
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <p className="text-sm text-gray-600">{brokerage.address}</p>
              <p className="text-sm text-gray-600">{brokerage.phone}</p>
              <p className="text-sm text-gray-600">{brokerage.email}</p>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Users</p>
                  <p className="font-medium text-gray-900">{brokerage.userCount}</p>
                </div>
                <div>
                  <p className="text-gray-500">Active Transactions</p>
                  <p className="font-medium text-gray-900">{brokerage.activeTransactions}</p>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-xs text-gray-500">Created: {brokerage.createdAt}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
