/**
 * Contacts page
 */

'use client';

import { useState } from 'react';
import { UserIcon, PhoneIcon, EnvelopeIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';

// Mock data
const mockContacts = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    role: 'buyer',
    company: 'ABC Realty',
    transactions: ['123 Main St', '456 Oak Ave'],
    avatar: null,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    role: 'seller',
    company: 'XYZ Properties',
    transactions: ['789 Pine St'],
    avatar: null,
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    role: 'lender',
    company: 'First National Bank',
    transactions: ['123 Main St', '789 Pine St'],
    avatar: null,
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    role: 'inspector',
    company: 'Professional Inspections',
    transactions: ['456 Oak Ave'],
    avatar: null,
  },
];

const roleColors = {
  buyer: 'text-blue-600 bg-blue-100',
  seller: 'text-green-600 bg-green-100',
  agent: 'text-purple-600 bg-purple-100',
  lender: 'text-orange-600 bg-orange-100',
  inspector: 'text-gray-600 bg-gray-100',
  attorney: 'text-red-600 bg-red-100',
};

export default function ContactsPage() {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredContacts = mockContacts.filter(contact => {
    const matchesFilter = filter === 'all' || contact.role === filter;
    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.company.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Contacts</h1>
            <p className="text-gray-600 mt-1">
              Manage your transaction contacts and relationships.
            </p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Add Contact
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex space-x-2">
            {['all', 'buyer', 'seller', 'agent', 'lender', 'inspector'].map((role) => (
              <button
                key={role}
                onClick={() => setFilter(role)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  filter === role
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {role.charAt(0).toUpperCase() + role.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Contacts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredContacts.map((contact) => (
          <div key={contact.id} className="bg-white shadow rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {contact.name}
                </h3>
                <span
                  className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                    roleColors[contact.role as keyof typeof roleColors]
                  }`}
                >
                  {contact.role}
                </span>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                <span className="truncate">{contact.email}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <PhoneIcon className="h-4 w-4 mr-2" />
                <span>{contact.phone}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                <span className="truncate">{contact.company}</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Active in {contact.transactions.length} transaction{contact.transactions.length !== 1 ? 's' : ''}
              </p>
              <div className="mt-1 flex flex-wrap gap-1">
                {contact.transactions.slice(0, 2).map((transaction, index) => (
                  <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {transaction}
                  </span>
                ))}
                {contact.transactions.length > 2 && (
                  <span className="text-xs text-gray-500">
                    +{contact.transactions.length - 2} more
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
