/**
 * Documents page
 */

'use client';

import { useState } from 'react';
import { DocumentTextIcon, FolderIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Mock data
const mockDocuments = [
  {
    id: '1',
    name: 'Purchase Agreement.pdf',
    type: 'contract',
    size: '2.4 MB',
    uploadedAt: '2024-01-10',
    transaction: '123 Main St',
    status: 'signed',
  },
  {
    id: '2',
    name: 'Inspection Report.pdf',
    type: 'inspection',
    size: '1.8 MB',
    uploadedAt: '2024-01-12',
    transaction: '123 Main St',
    status: 'pending',
  },
  {
    id: '3',
    name: 'Financing Pre-approval.pdf',
    type: 'financing',
    size: '856 KB',
    uploadedAt: '2024-01-08',
    transaction: '456 Oak Ave',
    status: 'approved',
  },
  {
    id: '4',
    name: 'Property Disclosure.pdf',
    type: 'disclosure',
    size: '1.2 MB',
    uploadedAt: '2024-01-14',
    transaction: '789 Pine St',
    status: 'review',
  },
];

const typeColors = {
  contract: 'text-blue-600 bg-blue-100',
  inspection: 'text-green-600 bg-green-100',
  financing: 'text-purple-600 bg-purple-100',
  disclosure: 'text-orange-600 bg-orange-100',
};

const statusColors = {
  signed: 'text-green-600 bg-green-100',
  pending: 'text-yellow-600 bg-yellow-100',
  approved: 'text-blue-600 bg-blue-100',
  review: 'text-orange-600 bg-orange-100',
};

export default function DocumentsPage() {
  const [filter, setFilter] = useState('all');

  const filteredDocuments = mockDocuments.filter(doc => {
    if (filter === 'all') return true;
    return doc.type === filter;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
            <p className="text-gray-600 mt-1">
              Manage transaction documents and files.
            </p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Upload Document
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex space-x-4">
          {['all', 'contract', 'inspection', 'financing', 'disclosure'].map((type) => (
            <button
              key={type}
              onClick={() => setFilter(type)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === type
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Documents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDocuments.map((doc) => (
          <div key={doc.id} className="bg-white shadow rounded-lg p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <DocumentTextIcon className="h-8 w-8 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {doc.name}
                  </h3>
                  <p className="text-sm text-gray-500">{doc.size}</p>
                </div>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <ArrowDownTrayIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between">
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    typeColors[doc.type as keyof typeof typeColors]
                  }`}
                >
                  {doc.type}
                </span>
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    statusColors[doc.status as keyof typeof statusColors]
                  }`}
                >
                  {doc.status}
                </span>
              </div>
              
              <div className="text-xs text-gray-500">
                <p>Transaction: {doc.transaction}</p>
                <p>Uploaded: {doc.uploadedAt}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
