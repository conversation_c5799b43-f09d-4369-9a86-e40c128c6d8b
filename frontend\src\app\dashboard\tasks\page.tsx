/**
 * Tasks page
 */

'use client';

import { useState } from 'react';
import { CheckIcon, ClockIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

// Mock data
const mockTasks = [
  {
    id: '1',
    title: 'Property Inspection',
    description: 'Schedule and coordinate property inspection for 123 Main St',
    dueDate: '2024-01-15',
    priority: 'high',
    status: 'pending',
    transaction: '123 Main St',
  },
  {
    id: '2',
    title: 'Financing Approval',
    description: 'Follow up on financing approval status',
    dueDate: '2024-01-18',
    priority: 'medium',
    status: 'in-progress',
    transaction: '456 Oak Ave',
  },
  {
    id: '3',
    title: 'Final Walkthrough',
    description: 'Coordinate final walkthrough with all parties',
    dueDate: '2024-01-20',
    priority: 'high',
    status: 'completed',
    transaction: '789 Pine St',
  },
];

const priorityColors = {
  high: 'text-red-600 bg-red-100',
  medium: 'text-yellow-600 bg-yellow-100',
  low: 'text-green-600 bg-green-100',
};

const statusIcons = {
  pending: ClockIcon,
  'in-progress': ExclamationTriangleIcon,
  completed: CheckIcon,
};

export default function TasksPage() {
  const [filter, setFilter] = useState('all');

  const filteredTasks = mockTasks.filter(task => {
    if (filter === 'all') return true;
    return task.status === filter;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900">Tasks</h1>
        <p className="text-gray-600 mt-1">
          Manage and track your transaction tasks.
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex space-x-4">
          {['all', 'pending', 'in-progress', 'completed'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === status
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
            </button>
          ))}
        </div>
      </div>

      {/* Tasks List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            {filteredTasks.length} Tasks
          </h2>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredTasks.map((task) => {
            const StatusIcon = statusIcons[task.status as keyof typeof statusIcons];
            return (
              <div key={task.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <StatusIcon className="h-5 w-5 text-gray-400" />
                      <h3 className="text-lg font-medium text-gray-900">
                        {task.title}
                      </h3>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          priorityColors[task.priority as keyof typeof priorityColors]
                        }`}
                      >
                        {task.priority}
                      </span>
                    </div>
                    <p className="text-gray-600 mt-1">{task.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Transaction: {task.transaction}</span>
                      <span>Due: {task.dueDate}</span>
                      <span className="capitalize">Status: {task.status.replace('-', ' ')}</span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
