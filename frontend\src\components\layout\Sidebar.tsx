/**
 * Sidebar navigation component
 */

'use client';

import { Fragment } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';
import {
  HomeIcon,
  DocumentTextIcon,
  CheckIcon,
  FolderIcon,
  UsersIcon,
  CogIcon,
  XMarkIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import { useAuthStore } from '@/store/authStore';
import { UserRole } from '@/types/auth';
import { cn } from '@/lib/utils';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
  { name: 'Transactions', href: '/dashboard/transactions', icon: DocumentTextIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
  { name: 'Tasks', href: '/dashboard/tasks', icon: CheckIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
  { name: 'Documents', href: '/dashboard/documents', icon: FolderIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
  { name: 'Contacts', href: '/dashboard/contacts', icon: UsersIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
  { name: 'Users', href: '/dashboard/users', icon: UsersIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER] },
  { name: 'Brokerages', href: '/dashboard/brokerages', icon: BuildingOfficeIcon, roles: [UserRole.ADMIN] },
  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon, roles: [UserRole.ADMIN, UserRole.TC, UserRole.BROKER, UserRole.AGENT] },
];

function NavigationItems() {
  const pathname = usePathname();
  const { user } = useAuthStore();

  const filteredNavigation = navigation.filter(item =>
    user && item.roles.includes(user.role)
  );

  return (
    <nav className="mt-5 flex-1 px-2 space-y-1">
      {filteredNavigation.map((item) => {
        const isActive = pathname === item.href;
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              isActive
                ? 'bg-primary-100 border-primary-500 text-primary-700'
                : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',
              'group flex items-center px-2 py-2 text-sm font-medium border-l-4 transition-colors'
            )}
          >
            <item.icon
              className={cn(
                isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500',
                'mr-3 h-6 w-6 transition-colors'
              )}
              aria-hidden="true"
            />
            {item.name}
          </Link>
        );
      })}
    </nav>
  );
}

export function Sidebar({ open, onClose }: SidebarProps) {
  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-40 md:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
          </Transition.Child>

          <div className="fixed inset-0 flex z-40">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute top-0 right-0 -mr-12 pt-2">
                    <button
                      type="button"
                      className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                      onClick={onClose}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                  <div className="flex-shrink-0 flex items-center px-4">
                    <h1 className="text-xl font-bold text-gray-900">TC Platform</h1>
                  </div>
                  <NavigationItems />
                </div>
              </Dialog.Panel>
            </Transition.Child>
            <div className="flex-shrink-0 w-14">{/* Force sidebar to shrink to fit close icon */}</div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white">
          <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <h1 className="text-xl font-bold text-gray-900">TC Platform</h1>
            </div>
            <NavigationItems />
          </div>
        </div>
      </div>
    </>
  );
}
